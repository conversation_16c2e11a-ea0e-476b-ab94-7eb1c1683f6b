package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// Port status constants
const (
	PortStatusUnknown = iota
	PortStatusUp
	PortStatusDown
	PortStatusError
)

// Tab constants
const (
	TabICI = iota
	TabDiag
)

// Tab styles
var (
	activeTabBorder = lipgloss.Border{
		Top:         "─",
		Bottom:      " ",
		Left:        "│",
		Right:       "│",
		TopLeft:     "╭",
		TopRight:    "╮",
		BottomLeft:  "┘",
		BottomRight: "└",
	}

	tabBorder = lipgloss.Border{
		Top:         "─",
		Bottom:      "─",
		Left:        "│",
		Right:       "│",
		TopLeft:     "╭",
		TopRight:    "╮",
		BottomLeft:  "┴",
		BottomRight: "┴",
	}

	tab = lipgloss.NewStyle().
		Border(tabBorder, true).
		BorderForeground(lipgloss.Color("39")).
		Padding(0, 1)

	activeTab = lipgloss.NewStyle().
			Border(activeTabBorder, true).
			BorderForeground(lipgloss.Color("226")).
			Padding(0, 1)

	tabGap = lipgloss.NewStyle().
		BorderTop(true).
		BorderTopForeground(lipgloss.Color("39")).
		BorderBottom(false).
		BorderLeft(false).
		BorderRight(false)
)

// QSFPPort represents a single QSFP port
type QSFPPort struct {
	ID     int
	Status int
	Info   string
}

// Model represents the main application state
type Model struct {
	ports          [16]QSFPPort
	selectedPort   int
	selectedPorts  map[int]bool // Track multiple selected ports
	diagnosticData string
	lastUpdate     time.Time
	width          int
	height         int
	mouseEnabled   bool
	isUpdating     bool
	updateCount    int
	machineInput   string
	inputFocused   bool
	selectedICIs   []string // Multiple ICI ports
	activeTab      int      // Current active tab (TabICI or TabDiag)
}

// Messages for the tea program
type tickMsg time.Time
type diagnosticMsg string

// Initialize the model
func initialModel() Model {
	ports := [16]QSFPPort{}
	for i := 0; i < 16; i++ {
		ports[i] = QSFPPort{
			ID:     i + 1,
			Status: PortStatusUnknown,
			Info:   fmt.Sprintf("Port %d", i+1),
		}
	}

	model := Model{
		ports:         ports,
		selectedPort:  0,
		selectedPorts: make(map[int]bool),
		mouseEnabled:  true,
		machineInput:  "",
		inputFocused:  false,
		selectedICIs:  []string{}, // Multiple ICI ports
		activeTab:     TabICI,     // Start with ICI tab active
	}
	// Select first port by default
	model.selectedPorts[0] = true
	model.updateSelectedICIs()
	return model
}

// Init initializes the program
func (m Model) Init() tea.Cmd {
	return tea.Batch(
		tea.EnableMouseCellMotion,
		// tickCmd(), // Temporarily disabled to debug
		// runDiagnosticCmd(), // Temporarily disabled to debug
	)
}

// Update handles messages
func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		return m, nil

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
		case "ctrl+i":
			// Switch to ICI tab (Ctrl+I)
			m.activeTab = TabICI
			m.inputFocused = false // Reset input focus when switching tabs
		case "ctrl+d":
			// Switch to Diag tab (Ctrl+D)
			m.activeTab = TabDiag
			m.inputFocused = false // Reset input focus when switching tabs
		case "tab":
			// Toggle between port selection and machine input (only in ICI tab)
			if m.activeTab == TabICI {
				m.inputFocused = !m.inputFocused
			}
		case "enter":
			if m.inputFocused {
				// Run test with current machine and selected ICI ports
				return m, runTestCmd(m.machineInput, strings.Join(m.selectedICIs, ","))
			}
		case "backspace":
			if m.inputFocused && len(m.machineInput) > 0 {
				m.machineInput = m.machineInput[:len(m.machineInput)-1]
			}
		case " ", "space":
			if !m.inputFocused {
				// Toggle selection of current port
				if m.selectedPorts[m.selectedPort] {
					delete(m.selectedPorts, m.selectedPort)
				} else {
					m.selectedPorts[m.selectedPort] = true
				}
				m.updateSelectedICIs()
			}
		case "left":
			if !m.inputFocused && m.selectedPort > 0 {
				m.selectedPort--
			}
		case "right":
			if !m.inputFocused && m.selectedPort < 15 {
				m.selectedPort++
			}
		case "home":
			if !m.inputFocused {
				m.selectedPort = 0
			}
		case "end":
			if !m.inputFocused {
				m.selectedPort = 15
			}
		default:
			// Handle text input for machine name
			if m.inputFocused && len(msg.String()) == 1 {
				char := msg.String()
				// Allow alphanumeric characters for machine names
				if (char >= "a" && char <= "z") || (char >= "A" && char <= "Z") || (char >= "0" && char <= "9") {
					m.machineInput += char
				}
			}
		}

	case tea.MouseMsg:
		// Handle multiple mouse event types for better compatibility
		isLeftClick := false

		// Check for different types of left click events
		if msg.Action == tea.MouseActionPress && msg.Button == tea.MouseButtonLeft {
			isLeftClick = true
		} else if msg.Action == tea.MouseActionRelease && msg.Button == tea.MouseButtonLeft {
			isLeftClick = true
		} else if msg.Type == tea.MouseLeft {
			isLeftClick = true
		}

		if isLeftClick {
			// Handle mouse clicks on ports
			portIndex := m.getPortFromMouse(msg.X, msg.Y)

			if portIndex >= 0 && portIndex < 16 {
				m.selectedPort = portIndex
				// Toggle selection on click
				if m.selectedPorts[portIndex] {
					delete(m.selectedPorts, portIndex)
				} else {
					m.selectedPorts[portIndex] = true
				}
				m.updateSelectedICIs()
			}
		}

	case tickMsg:
		// DEBUG: Disabled for testing
		// m.lastUpdate = time.Time(msg)
		// m.isUpdating = true
		// m.updateCount++
		// return m, tea.Batch(tickCmd(), runDiagnosticCmd())

	case diagnosticMsg:
		// DEBUG: Disabled for testing
		// m.diagnosticData = string(msg)
		// m.processDiagnosticData()
		// m.isUpdating = false
	}

	return m, nil
}

// View renders the UI
func (m Model) View() string {
	if m.width == 0 || m.height == 0 {
		return "Loading..."
	}

	var b strings.Builder

	// Title
	title := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("86")).
		Width(m.width).
		Align(lipgloss.Center).
		Render("ICI Network Diagnostics")
	b.WriteString(title + "\n")

	// Render tabs
	tabsContent := m.renderTabs()
	b.WriteString(tabsContent + "\n")

	// Content based on active tab
	if m.activeTab == TabICI {
		b.WriteString("\n=== ICI TAB ===\n")
		b.WriteString("Original network switch view will be here\n")
		b.WriteString("• Switch visualization\n")
		b.WriteString("• Port selection\n")
		b.WriteString("• Control panel\n")
	} else {
		b.WriteString("\n=== DIAG TAB ===\n")
		b.WriteString("Diagnostic view placeholder\n")
		b.WriteString("• Advanced diagnostics\n")
		b.WriteString("• Performance metrics\n")
		b.WriteString("• Historical data\n")
	}

	// Footer
	b.WriteString("\n\nControls: Ctrl+I (ICI tab) | Ctrl+D (Diag tab) | q (quit)")

	return b.String()
}

// renderTabs creates the tab bar
func (m Model) renderTabs() string {
	var iciTabStyle, diagTabStyle lipgloss.Style

	if m.activeTab == TabICI {
		iciTabStyle = activeTab
		diagTabStyle = tab
	} else {
		iciTabStyle = tab
		diagTabStyle = activeTab
	}

	row := lipgloss.JoinHorizontal(
		lipgloss.Top,
		iciTabStyle.Render("ICI"),
		diagTabStyle.Render("Diag"),
	)

	// Fill remaining space with gap
	gap := tabGap.Render(strings.Repeat("─", max(0, m.width-lipgloss.Width(row)-2)))
	row = lipgloss.JoinHorizontal(lipgloss.Bottom, row, gap)

	return row
}

// renderICIView renders the original ICI view with switch and control panel
func (m Model) renderICIView(diagnosticHeight int) string {
	var b strings.Builder

	// Switch visualization - full width
	b.WriteString(m.renderSwitch())
	b.WriteString("\n")

	// Selected port info - full width
	selectedPortInfo := m.renderSelectedPortInfo()
	b.WriteString(selectedPortInfo)
	b.WriteString("\n")

	// Create side-by-side layout: Diagnostic output (left) and Control panel (right)
	diagnosticOutput := m.renderDiagnosticOutput(diagnosticHeight)
	controlPanel := m.renderControlPanel(diagnosticHeight)

	// Join them horizontally (no gap needed)
	sideByLayout := lipgloss.JoinHorizontal(lipgloss.Top, diagnosticOutput, controlPanel)
	b.WriteString(sideByLayout)

	return b.String()
}

// renderDiagView renders the diagnostic-focused view
func (m Model) renderDiagView(diagnosticHeight int) string {
	// Create a placeholder diagnostic view with full width
	diagStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("39")).
		Width(m.width - 4).
		Height(diagnosticHeight).
		Padding(1)

	placeholderContent := lipgloss.NewStyle().
		Foreground(lipgloss.Color("241")).
		Render("Diagnostic View - Coming Soon\n\nThis tab will contain:\n• Advanced network diagnostics\n• Detailed port analysis\n• Historical data\n• Performance metrics\n• Troubleshooting tools")

	return diagStyle.Render(placeholderContent)
}

// renderSwitch creates the visual representation of the switch
func (m Model) renderSwitch() string {
	var switchParts []string

	// Switch header
	switchHeader := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")).
		Width(m.width - 4). // Account for border
		Align(lipgloss.Center).
		Render("ICI Network Switch - 16x QSFP Ports")

	switchParts = append(switchParts, switchHeader)
	switchParts = append(switchParts, "")

	// Create horizontal layout with proper QSFP ports (1 high x 4 wide)
	var portBlocks []string

	for i := 0; i < 16; i++ {
		portBlock := m.renderQSFPPort(i)
		portBlocks = append(portBlocks, portBlock)

		// Add extra spacing after ports 4, 8, and 12 (0-indexed: 3, 7, 11)
		if i == 3 || i == 7 || i == 11 {
			// Add a spacer block for visual separation
			spacer := lipgloss.NewStyle().
				Width(2).
				Height(3).
				Render("  ")
			portBlocks = append(portBlocks, spacer)
		}
	}

	// Use Lipgloss to join ports horizontally (handles multi-line borders properly)
	portsDisplay := lipgloss.JoinHorizontal(lipgloss.Top, portBlocks...)

	// Create a bordered container for all ports with padding
	portsContainer := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("39")). // Blue border
		Padding(1, 2).                          // Vertical padding: 1, Horizontal padding: 2
		Align(lipgloss.Center).
		Render(portsDisplay)

	// Center the entire ports container in the switch area
	centeredContainer := lipgloss.NewStyle().
		Width(m.width - 6). // Account for switch border
		Align(lipgloss.Center).
		Render(portsContainer)

	// Add the centered ports container
	switchParts = append(switchParts, "QSFP Ports:")
	switchParts = append(switchParts, centeredContainer)

	// Port legend
	legend := m.renderPortLegend()
	switchParts = append(switchParts, "")
	switchParts = append(switchParts, legend)

	switchStyle := lipgloss.NewStyle().
		Border(lipgloss.DoubleBorder()).
		BorderForeground(lipgloss.Color("62")).
		Width(m.width-2). // Account for border width
		Padding(1, 2)

	return switchStyle.Render(strings.Join(switchParts, "\n"))
}

// renderSinglePort renders a single QSFP port (4 chars wide, 2 lines high)
func (m Model) renderSinglePort(portIndex int) string {
	port := m.ports[portIndex]

	// Get colors and indicators based on status
	var bgColor, fgColor, statusIndicator string

	switch port.Status {
	case PortStatusUp:
		bgColor = "22" // Dark green background
		fgColor = "46" // Bright green text
		statusIndicator = "●●●●"
	case PortStatusDown:
		bgColor = "52"  // Dark red background
		fgColor = "196" // Bright red text
		statusIndicator = "○○○○"
	case PortStatusError:
		bgColor = "94"  // Dark orange background
		fgColor = "208" // Bright orange text
		statusIndicator = "▲▲▲▲"
	default:
		bgColor = "235" // Dark gray background
		fgColor = "250" // Light gray text
		statusIndicator = "----"
	}

	// Create the base style for the port
	baseStyle := lipgloss.NewStyle().
		Width(4).
		Align(lipgloss.Center).
		Background(lipgloss.Color(bgColor)).
		Foreground(lipgloss.Color(fgColor))

	// Add selection highlighting with border
	if portIndex == m.selectedPort {
		baseStyle = baseStyle.Border(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color("226"))
	}

	// Format port number to fit in 4 characters
	portNum := fmt.Sprintf("%2d", port.ID)

	// Create a 2-line port block
	portContent := portNum + "\n" + statusIndicator

	return baseStyle.Render(portContent)
}

// getSimplePortStyle returns a simple text style for ports
func (m Model) getSimplePortStyle(portIndex int) lipgloss.Style {
	var bgColor, fgColor string

	switch m.ports[portIndex].Status {
	case PortStatusUp:
		bgColor = "22" // Dark green background
		fgColor = "46" // Bright green text
	case PortStatusDown:
		bgColor = "52"  // Dark red background
		fgColor = "196" // Bright red text
	case PortStatusError:
		bgColor = "94"  // Dark orange background
		fgColor = "208" // Bright orange text
	default:
		bgColor = "235" // Dark gray background
		fgColor = "250" // Light gray text
	}

	baseStyle := lipgloss.NewStyle().
		Background(lipgloss.Color(bgColor)).
		Foreground(lipgloss.Color(fgColor))

	// Add selection highlighting
	if portIndex == m.selectedPort {
		baseStyle = baseStyle.Bold(true).
			Foreground(lipgloss.Color("226"))
	}

	return baseStyle
}

// renderQSFPPort renders a single QSFP port (1 high x 4 wide) as border only
func (m Model) renderQSFPPort(portIndex int) string {
	port := m.ports[portIndex]

	// Get border color based on status
	var borderColor string
	switch port.Status {
	case PortStatusUp:
		borderColor = "46" // Green border
	case PortStatusDown:
		borderColor = "196" // Red border
	case PortStatusError:
		borderColor = "208" // Orange border
	default:
		borderColor = "240" // Gray border
	}

	// Create port content (port number)
	portNum := fmt.Sprintf("%2d", port.ID)

	// Create the port with Lipgloss border - exactly 1 high x 4 wide
	portStyle := lipgloss.NewStyle().
		Width(4).
		Height(1).
		Align(lipgloss.Center).
		Border(lipgloss.NormalBorder()).
		BorderForeground(lipgloss.Color(borderColor))

	// Add highlighting based on selection state
	if portIndex == m.selectedPort {
		// Current cursor position - yellow border
		portStyle = portStyle.BorderForeground(lipgloss.Color("226")) // Yellow for cursor
	}

	// Add selection highlighting - green background for selected ports
	if m.selectedPorts[portIndex] {
		portStyle = portStyle.Background(lipgloss.Color("22")) // Dark green background for selected
	}

	return portStyle.Render(portNum)
}

// getConnectionIndicator returns a visual indicator for port connection status (4 chars)
func (m Model) getConnectionIndicator(status int) string {
	switch status {
	case PortStatusUp:
		return "●●●●" // Connected - 4 chars
	case PortStatusDown:
		return "○○○○" // Disconnected - 4 chars
	case PortStatusError:
		return "▲▲▲▲" // Error - 4 chars
	default:
		return "----" // Unknown - 4 chars
	}
}

// renderPortLegend shows the status legend
func (m Model) renderPortLegend() string {
	legendItems := []string{
		lipgloss.NewStyle().Foreground(lipgloss.Color("46")).Render("●●●● UP"),
		lipgloss.NewStyle().Foreground(lipgloss.Color("196")).Render("○○○○ DOWN"),
		lipgloss.NewStyle().Foreground(lipgloss.Color("208")).Render("▲▲▲▲ ERROR"),
		lipgloss.NewStyle().Foreground(lipgloss.Color("240")).Render("---- UNKNOWN"),
	}

	legendText := "Legend: " + strings.Join(legendItems, " | ")

	return lipgloss.NewStyle().
		Width(m.width - 6). // Account for switch border and padding
		Align(lipgloss.Center).
		Render(legendText)
}

// getPortStyle returns the appropriate style for a port (4 chars wide, 1 char high)
func (m Model) getPortStyle(portIndex int) lipgloss.Style {
	baseStyle := lipgloss.NewStyle().
		Width(4).  // Exactly 4 characters wide
		Height(1). // Exactly 1 character high
		Align(lipgloss.Center).
		Border(lipgloss.NormalBorder()).
		Padding(0)

	// Selected port highlighting
	if portIndex == m.selectedPort {
		baseStyle = baseStyle.BorderForeground(lipgloss.Color("226")).
			BorderStyle(lipgloss.ThickBorder())
	} else {
		baseStyle = baseStyle.BorderForeground(lipgloss.Color("240"))
	}

	// Status coloring - more subtle background colors
	switch m.ports[portIndex].Status {
	case PortStatusUp:
		return baseStyle.Background(lipgloss.Color("22")).Foreground(lipgloss.Color("46"))
	case PortStatusDown:
		return baseStyle.Background(lipgloss.Color("52")).Foreground(lipgloss.Color("196"))
	case PortStatusError:
		return baseStyle.Background(lipgloss.Color("94")).Foreground(lipgloss.Color("208"))
	default:
		return baseStyle.Background(lipgloss.Color("235")).Foreground(lipgloss.Color("250"))
	}
}

// renderSelectedPortInfo shows details about the selected port
func (m Model) renderSelectedPortInfo() string {
	port := m.ports[m.selectedPort]

	statusText := "Unknown"
	statusColor := "240"

	switch port.Status {
	case PortStatusUp:
		statusText = "UP"
		statusColor = "46"
	case PortStatusDown:
		statusText = "DOWN"
		statusColor = "196"
	case PortStatusError:
		statusText = "ERROR"
		statusColor = "208"
	}

	// Show current cursor port and all selected ports
	selectedPortsList := make([]string, 0)
	for portIdx := range m.selectedPorts {
		selectedPortsList = append(selectedPortsList, fmt.Sprintf("%d", portIdx+1))
	}

	var selectedInfo string
	if len(selectedPortsList) > 0 {
		selectedInfo = fmt.Sprintf("Selected Ports: [%s]", strings.Join(selectedPortsList, ","))
	} else {
		selectedInfo = "No ports selected"
	}

	info := fmt.Sprintf("Cursor: Port %d | %s | Status: %s | %s",
		port.ID, selectedInfo, statusText, port.Info)

	style := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color(statusColor)).
		Width(m.width-2). // Account for border width
		Padding(0, 1).
		Align(lipgloss.Center)

	return style.Render(info)
}

// renderDiagnosticOutput shows the diagnostic information (left side)
func (m Model) renderDiagnosticOutput(height int) string {
	// Ensure minimum height
	if height < 5 {
		height = 5
	}

	// Calculate width to match control panel calculation exactly
	// Account for borders: each panel has 2 chars for left+right borders
	availableWidth := m.width - 4 // Account for both panel borders (2 chars each)

	diagnosticWidth := (availableWidth * 60) / 100 // Use 60% of available space
	if diagnosticWidth < 40 {
		diagnosticWidth = 40 // Minimum width
	}

	style := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("39")).
		Padding(1).
		Height(height).
		Width(diagnosticWidth)

	content := m.diagnosticData
	if content == "" {
		content = "Running diagnostics..."
	}

	// Truncate content if it's too long for the available space
	lines := strings.Split(content, "\n")
	maxLines := height - 2 // Account for padding
	if len(lines) > maxLines {
		lines = lines[len(lines)-maxLines:] // Show most recent lines
		content = strings.Join(lines, "\n")
	}

	return style.Render(content)
}

// renderControlPanel shows the machine input and selected ICI ports (right side)
func (m Model) renderControlPanel(height int) string {
	// Ensure minimum height
	if height < 5 {
		height = 5
	}

	// Calculate widths more carefully to prevent overflow
	// Account for borders: each panel has 2 chars for left+right borders
	availableWidth := m.width - 4 // Account for both panel borders (2 chars each)

	diagnosticWidth := (availableWidth * 60) / 100   // Use 60% of available space
	controlWidth := availableWidth - diagnosticWidth // Remaining space

	// Ensure minimum widths
	if diagnosticWidth < 40 {
		diagnosticWidth = 40
		controlWidth = availableWidth - diagnosticWidth
	}
	if controlWidth < 25 {
		controlWidth = 25
		diagnosticWidth = availableWidth - controlWidth
	}

	var controlParts []string

	// Machine name input section
	controlParts = append(controlParts, "Machine Configuration")
	controlParts = append(controlParts, "")

	// Machine input field
	var machineInputContent string
	if m.inputFocused {
		machineInputContent = fmt.Sprintf("Machine: %s█", m.machineInput) // Show cursor
	} else {
		if m.machineInput == "" {
			machineInputContent = "Machine: (Tab to focus)"
		} else {
			machineInputContent = fmt.Sprintf("Machine: %s", m.machineInput)
		}
	}

	machineStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("39")).
		Padding(0, 1)

	if m.inputFocused {
		machineStyle = machineStyle.BorderForeground(lipgloss.Color("226")) // Yellow when focused
	}

	controlParts = append(controlParts, machineStyle.Render(machineInputContent))
	controlParts = append(controlParts, "")

	// Selected ICI ports section
	controlParts = append(controlParts, "Selected ICI Ports")
	controlParts = append(controlParts, "")

	if len(m.selectedICIs) == 0 {
		controlParts = append(controlParts, "No ports selected")
		controlParts = append(controlParts, "Use Space or click to select QSFP ports")
	} else {
		for i, ici := range m.selectedICIs {
			portNum := i + 1
			for portIdx := range m.selectedPorts {
				if fmt.Sprintf("ici%d", portIdx+1) == ici {
					portNum = portIdx + 1
					break
				}
			}
			iciLine := fmt.Sprintf("• QSFP Port %d → %s", portNum, ici)
			controlParts = append(controlParts, iciLine)
		}
	}

	controlParts = append(controlParts, "")

	// Test execution section
	if m.machineInput != "" && len(m.selectedICIs) > 0 {
		controlParts = append(controlParts, "✅ Ready to test!")
		controlParts = append(controlParts, "Press Enter to run test")
	} else {
		controlParts = append(controlParts, "Need machine name and ports")
	}

	// Create the control panel
	content := strings.Join(controlParts, "\n")

	style := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("46")). // Green border
		Padding(1).
		Height(height).
		Width(controlWidth)

	return style.Render(content)
}

// getPortFromMouse calculates which port was clicked based on mouse coordinates
func (m Model) getPortFromMouse(x, y int) int {
	// Check if click is in the port area (more generous vertical bounds)
	if y < 7 || y > 15 {
		return -1
	}

	// More accurate horizontal detection accounting for port grouping
	// The ports are centered in the container with padding

	// Estimate the starting position of the ports
	// Each port is about 6 characters wide (including border)
	// Groups: [1-4] GAP [5-8] GAP [9-12] GAP [13-16]
	// Total width approximately: 4*6 + 2 + 4*6 + 2 + 4*6 + 2 + 4*6 = 104 chars

	totalPortsWidth := 104
	startX := (m.width - totalPortsWidth) / 2

	if x < startX || x > startX+totalPortsWidth {
		return -1
	}

	relativeX := x - startX

	// Map to port groups with gaps
	var portIndex int = -1

	if relativeX < 24 {
		// Group 1: ports 0-3
		portIndex = relativeX / 6
	} else if relativeX < 26 {
		// Gap between group 1 and 2
		return -1
	} else if relativeX < 50 {
		// Group 2: ports 4-7
		portIndex = 4 + (relativeX-26)/6
	} else if relativeX < 52 {
		// Gap between group 2 and 3
		return -1
	} else if relativeX < 76 {
		// Group 3: ports 8-11
		portIndex = 8 + (relativeX-52)/6
	} else if relativeX < 78 {
		// Gap between group 3 and 4
		return -1
	} else if relativeX < 102 {
		// Group 4: ports 12-15
		portIndex = 12 + (relativeX-78)/6
	}

	// Validate port index
	if portIndex >= 0 && portIndex < 16 {
		return portIndex
	}

	return -1
}

// processDiagnosticData processes the raw diagnostic output
func (m *Model) processDiagnosticData() {
	lines := strings.Split(m.diagnosticData, "\n")

	// Parse interface status information
	for _, line := range lines {
		// Parse interface lines like "3: ici0: <BROADCAST,MULTICAST,UP,LOWER_UP>"
		if strings.Contains(line, "ici") && strings.Contains(line, ":") {
			portNum := m.extractPortNumber(line)
			if portNum >= 0 && portNum < 16 {
				if strings.Contains(line, "UP,LOWER_UP") {
					m.ports[portNum].Status = PortStatusUp
					m.ports[portNum].Info = "Link UP - Active"
				} else if strings.Contains(line, "DOWN") {
					m.ports[portNum].Status = PortStatusDown
					m.ports[portNum].Info = "Link DOWN - Inactive"
				} else {
					m.ports[portNum].Status = PortStatusError
					m.ports[portNum].Info = "Link ERROR - Check connection"
				}
			}
		}

		// Parse network statistics for error detection
		if strings.Contains(line, "ici") && strings.Contains(line, "errs") {
			fields := strings.Fields(line)
			if len(fields) >= 4 {
				portNum := m.extractPortNumberFromInterface(fields[0])
				if portNum >= 0 && portNum < 16 {
					// Check for errors in receive/transmit
					if fields[3] != "0" || (len(fields) >= 10 && fields[9] != "0") {
						if m.ports[portNum].Status == PortStatusUp {
							m.ports[portNum].Status = PortStatusError
							m.ports[portNum].Info = "Link UP - Errors detected"
						}
					}
				}
			}
		}
	}

	// Simulate some ports for demonstration if no real data
	if !strings.Contains(m.diagnosticData, "ici") {
		m.simulatePortStatuses()
	}
}

// extractPortNumber extracts port number from interface description
func (m Model) extractPortNumber(line string) int {
	// Look for patterns like "ici0:", "ici1:", etc.
	if idx := strings.Index(line, "ici"); idx != -1 {
		start := idx + 3
		end := start
		for end < len(line) && line[end] >= '0' && line[end] <= '9' {
			end++
		}
		if end > start {
			if portStr := line[start:end]; len(portStr) > 0 {
				var portNum int
				if n, _ := fmt.Sscanf(portStr, "%d", &portNum); n == 1 {
					return portNum
				}
			}
		}
	}
	return -1
}

// extractPortNumberFromInterface extracts port number from interface name in stats
func (m Model) extractPortNumberFromInterface(interfaceName string) int {
	interfaceName = strings.TrimSuffix(interfaceName, ":")
	if strings.HasPrefix(interfaceName, "ici") {
		portStr := strings.TrimPrefix(interfaceName, "ici")
		var portNum int
		if n, _ := fmt.Sscanf(portStr, "%d", &portNum); n == 1 {
			return portNum
		}
	}
	return -1
}

// simulatePortStatuses creates realistic port statuses for demonstration
func (m *Model) simulatePortStatuses() {
	// Simulate a realistic network scenario
	statuses := []int{
		PortStatusUp, PortStatusUp, PortStatusDown, PortStatusUp,
		PortStatusUp, PortStatusError, PortStatusUp, PortStatusDown,
		PortStatusUp, PortStatusUp, PortStatusUp, PortStatusDown,
		PortStatusError, PortStatusUp, PortStatusDown, PortStatusUp,
	}

	infos := []string{
		"Active - 10Gbps", "Active - 10Gbps", "No Link", "Active - 10Gbps",
		"Active - 25Gbps", "CRC Errors", "Active - 10Gbps", "Cable Fault",
		"Active - 40Gbps", "Active - 10Gbps", "Active - 25Gbps", "No Signal",
		"Temp Warning", "Active - 10Gbps", "Disconnected", "Active - 40Gbps",
	}

	for i := 0; i < 16; i++ {
		m.ports[i].Status = statuses[i]
		m.ports[i].Info = infos[i]
	}
}

// Commands
func tickCmd() tea.Cmd {
	return tea.Tick(time.Second*5, func(t time.Time) tea.Msg {
		return tickMsg(t)
	})
}

func runDiagnosticCmd() tea.Cmd {
	return func() tea.Msg {
		var output strings.Builder
		timestamp := time.Now().Format("15:04:05")
		output.WriteString(fmt.Sprintf("=== ICI Network Test Suite - %s ===\n\n", timestamp))

		// Generate 5 example tests with machine names and ICI ports
		tests := []struct {
			name        string
			machine     string
			iciPorts    string
			status      string
			description string
		}{
			{"Bandwidth Stress Test", "aabbc12", "ici0,ici1", "RUNNING", "Testing 40Gbps throughput on dual ports"},
			{"Latency Measurement", "xxyyz34", "ici2", "PASSED", "Sub-microsecond latency verification"},
			{"Error Rate Analysis", "mmnnp56", "ici0-ici3", "FAILED", "Packet loss detected on port ici2"},
			{"Link Stability Test", "qqrrs78", "ici1", "PASSED", "24-hour continuous operation test"},
			{"Optical Power Check", "ttuvw90", "ici0,ici2", "WARNING", "Port ici2 power below threshold"},
		}

		output.WriteString("--- Active Test Results ---\n")
		for i, test := range tests {
			statusColor := ""
			switch test.status {
			case "PASSED":
				statusColor = "✓"
			case "FAILED":
				statusColor = "✗"
			case "RUNNING":
				statusColor = "⟳"
			case "WARNING":
				statusColor = "⚠"
			}

			output.WriteString(fmt.Sprintf("%d. %s [%s]\n", i+1, test.name, test.machine))
			output.WriteString(fmt.Sprintf("   Ports: %s | Status: %s %s\n", test.iciPorts, statusColor, test.status))
			output.WriteString(fmt.Sprintf("   %s\n\n", test.description))
		}

		// Add some system info
		output.WriteString("--- System Information ---\n")
		output.WriteString("ICI Driver Version: 2.1.4\n")
		output.WriteString("Firmware: 1.8.2\n")
		output.WriteString("Active Connections: 12/16 ports\n")
		output.WriteString("Total Throughput: 320 Gbps\n")
		output.WriteString("Uptime: 15d 8h 42m\n\n")

		output.WriteString("--- Recent Events ---\n")
		output.WriteString("03:57:35 - Port ici2 on mmnnp56: CRC error detected\n")
		output.WriteString("03:57:28 - Test 'Bandwidth Stress Test' started on aabbc12\n")
		output.WriteString("03:57:15 - Port ici1 on qqrrs78: Link stability test completed\n")
		output.WriteString("03:57:02 - System health check: All critical systems operational\n")

		return diagnosticMsg(output.String())
	}
}

// getSimulatedDiagnosticData provides realistic simulated data when actual commands fail
func getSimulatedDiagnosticData(commandType string) string {
	switch commandType {
	case "Interface Status":
		return `1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN
2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc pfifo_fast state UP
3: ici0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 9000 qdisc mq state UP
4: ici1: <BROADCAST,MULTICAST,DOWN> mtu 9000 qdisc mq state DOWN
`
	case "Network Statistics":
		return `Inter-|   Receive                                                |  Transmit
 face |bytes    packets errs drop fifo frame compressed multicast|bytes    packets errs drop fifo colls carrier compressed
    lo:    1234      12    0    0    0     0          0         0     1234      12    0    0    0     0       0          0
  eth0: 1234567    1234    0    0    0     0          0         0  1234567    1234    0    0    0     0       0          0
  ici0: 9876543    9876    0    0    0     0          0         0  9876543    9876    0    0    0     0       0          0
  ici1:       0       0    0    0    0     0          0         0        0       0    0    0    0     0       0          0
`
	case "Ethernet Tool Info":
		return `driver: ici_driver
version: 1.2.3
firmware-version: 4.5.6
expansion-rom-version:
bus-info: 0000:01:00.0
supports-statistics: yes
supports-test: yes
supports-eeprom-access: yes
supports-register-dump: yes
supports-priv-flags: yes
`
	default:
		return "Simulated diagnostic data for " + commandType + "\n"
	}
}

// updateSelectedICIs updates the selected ICI ports based on the selected QSFP ports
func (m *Model) updateSelectedICIs() {
	// Clear existing selections
	m.selectedICIs = make([]string, 0)

	// Convert selected QSFP ports to ICI ports
	for portIdx := range m.selectedPorts {
		iciPort := fmt.Sprintf("ici%d", portIdx+1)
		m.selectedICIs = append(m.selectedICIs, iciPort)
	}
}

// runTestCmd runs a test command with the specified machine and ICI port
func runTestCmd(machine, iciPort string) tea.Cmd {
	return func() tea.Msg {
		var output strings.Builder
		timestamp := time.Now().Format("15:04:05")

		if machine == "" {
			output.WriteString(fmt.Sprintf("=== Test Error - %s ===\n\n", timestamp))
			output.WriteString("ERROR: Machine name is required\n")
			output.WriteString("Please enter a machine name (e.g., aabbc12) and try again.\n")
		} else {
			output.WriteString(fmt.Sprintf("=== Running Test on %s - %s ===\n\n", machine, timestamp))
			output.WriteString(fmt.Sprintf("Target Machine: %s\n", machine))
			output.WriteString(fmt.Sprintf("Selected ICI Port: %s\n\n", iciPort))

			// Simulate test execution
			output.WriteString("--- Test Execution ---\n")
			output.WriteString(fmt.Sprintf("Connecting to %s...\n", machine))
			output.WriteString(fmt.Sprintf("Initializing %s interface...\n", iciPort))
			output.WriteString("Running bandwidth test...\n")
			output.WriteString("Measuring latency...\n")
			output.WriteString("Checking error rates...\n\n")

			output.WriteString("--- Test Results ---\n")
			output.WriteString("✓ Connection: ESTABLISHED\n")
			output.WriteString("✓ Bandwidth: 25.6 Gbps\n")
			output.WriteString("✓ Latency: 0.8 μs\n")
			output.WriteString("✓ Error Rate: 0.00%\n")
			output.WriteString("✓ Status: PASSED\n\n")

			output.WriteString(fmt.Sprintf("Test completed successfully on %s:%s\n", machine, iciPort))
		}

		return diagnosticMsg(output.String())
	}
}

// max returns the maximum of two integers
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func main() {
	fmt.Println("Starting TUI...")
	model := initialModel()
	fmt.Printf("Model created: %+v\n", model)

	p := tea.NewProgram(model, tea.WithAltScreen(), tea.WithMouseAllMotion())
	fmt.Println("Program created, starting...")
	if _, err := p.Run(); err != nil {
		log.Fatal(err)
	}
	fmt.Println("Program ended")
}
